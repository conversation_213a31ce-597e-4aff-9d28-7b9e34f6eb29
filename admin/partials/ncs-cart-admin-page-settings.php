<?php
/**
 * Provide a admin area view for the plugin
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://studiocart.co
 * @since      1.0.0
 *
 * @package    Now Hiring
 * @subpackage Now Hiring/admin/partials
 */
?>
<?php
foreach (['sc_stripe_settings_error','sc_express_payment_settings_error','sc_customer_portal_settings_error'] as $transient) {
    if ($notice = get_transient($transient)) {
        ?>
        <div class="notice notice-<?php echo esc_attr($notice['type'] ?? 'error'); ?> is-dismissible">
            <p><?php echo esc_html($notice['message']); ?></p>
        </div>
        <?php
    }
}
?>
<h2><?php echo esc_html( get_admin_page_title() ); ?></h2>
<?php
$active_tab = 'general';
if(isset($_REQUEST['tab'])){
	$active_tab = $_REQUEST['tab'];
} 

$tabs = apply_filters('sc_setting_tabs', array(
    'general' => __('General', 'sandbox'),
    'payment_methods' => __('Payment Methods', 'sandbox'),
    'integrations' => __('Integrations', 'sandbox'),
    'emails' => __('Emails', 'sandbox'),
    'tax' => __('Taxes', 'sandbox'),
    'invoice' => __('Invoices', 'sandbox'),
    // Add more tabs as needed
));

echo '<div class="nav-tab-wrapper">';
foreach ($tabs as $tab_slug => $tab_name) {
    echo '<a href="#' . esc_attr($tab_slug) . '" id="settings_tab_' . esc_attr($tab_slug) . '" onclick="ncs_settings(\'' . esc_js($tab_slug) . '\');" class="settings_tab nav-tab">' . esc_html($tab_name) . '</a>';
}
echo '</div>';

?>
<form method="post" action="options.php" class="settings-options-form"><?php
	settings_fields( $this->plugin_name . '-settings' );

	$tab_contents = apply_filters('sc_setting_tabs', array(
		'general' => __('', 'sandbox'),
		'payment_methods' => __('payment', 'sandbox'),
		'integrations' => __('integrations', 'sandbox'),
		'emails' => __('email', 'sandbox'),
		'tax' => __('tax', 'sandbox'),
		'invoice' => __('invoice', 'sandbox'),
	));
	
	
	foreach ($tab_contents as $content_id => $sec_name) {?>
		<div id="content_tab_<?php _e( $content_id );?>" class="tab-content" style="display:none"><?php 
			if( $sec_name == '' ){
				do_settings_sections( $this->plugin_name);
			} else {
				if( $sec_name == 'tax' ){
					include( plugin_dir_path( __FILE__ ) . 'ncs-cart-admin-page-settings-tax.php' ); 
				}
				do_settings_sections( $this->plugin_name.'-'.strtolower($sec_name));
			} 
			?>
		</div><?php 
	};?>

<?php submit_button( __('Save Settings', 'ncs-cart') );
?></form>